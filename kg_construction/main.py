# Anything related to using a CustomNebulaPropertyGraphStore instance is put here. 
# Will open a new connection each time
# Will throw error out since request will fail if the knowledge graph operation fail. 

from .CustomNebulaPropertyGraphStore import CustomNebulaPropertyGraphStore
from .mock_data import generate_mock
from .mock_data2 import generate_mock as generate_mock2
from .RelationshipFinder import RelationshipFinder
import nest_asyncio
nest_asyncio.apply()
import time
from .CustomNodes import *

def insert_chunks(nodes:list[LabelledNode],selected_dataset, create_time:Optional[float] = None):
    start_time = time.time()


    relationships = RelationshipFinder.find_all_relationship(nodes)
    # entities = RelationshipFinder.find_all_entities(nodes)  # 注释掉关键词提取功能
    entities = []  # 设置为空列表，跳过关键词提取
    print("ENTITIES ARE: ", entities)
    print("RELATIONSHIP ARE: ", relationships)

    # Wait if the space is newly created
    if create_time:
        pass_time = time.time()-create_time
        print("seconds since creation of space: ",pass_time," seconds")
        if pass_time < 10:
            print("waiting", 10 -pass_time, " seconds")
            time.sleep(10 - pass_time)
    

    graph_store = CustomNebulaPropertyGraphStore(
        space=selected_dataset, overwrite=False
    )
    graph_store.build_nodes(nodes) 
    graph_store.build_parent(nodes)
    graph_store.build_follow(nodes)
    graph_store.build_entities(entities)    
    graph_store.build_relationships(relationships)

    print("Total time used in insert_chunk module: ",time.time()-start_time, "seconds")
    graph_store._client.close()

#A method called by programs outside this module, have same function as delete_chunks right now. 
def delete_chunks(id:str,selected_dataset):
    graph_store = CustomNebulaPropertyGraphStore(
        space=selected_dataset, overwrite=False
    )
    
    graph_store.delete_document(id)
    graph_store._client.close()



def create_space(selected_dataset:str):
    graph_store = CustomNebulaPropertyGraphStore(space="default",overwrite=False)
    graph_store.structured_query("CREATE SPACE "+selected_dataset+" AS default")
    graph_store._client.close()



def delete_space(selected_dataset:str):
    graph_store = CustomNebulaPropertyGraphStore(space="default",overwrite=False)
    graph_store.structured_query("DROP SPACE "+selected_dataset)
    graph_store._client.close()


def clear_space(selected_dataset:str):
    graph_store = CustomNebulaPropertyGraphStore(space="default",overwrite=False)
    graph_store.structured_query("CLEAR SPACE "+selected_dataset)
    graph_store._client.close()

def search_node(id, selected_dataset):
    graph_store = CustomNebulaPropertyGraphStore(space=selected_dataset,overwrite=False)
    result = graph_store.structured_query(f"match (n) where id(n) == \"{id}\" return n;")
    graph_store._client.close()
    return result

def search_follow_neighbour(id, selected_dataset):
    graph_store = CustomNebulaPropertyGraphStore(space=selected_dataset,overwrite=False)
    following_node_result = graph_store.structured_query(f"GO 1 STEPS FROM \"{id}\" OVER followed_by REVERSELY YIELD dst(edge) AS dst, src(edge) AS following")
    followed_node_result = graph_store.structured_query(f"GO 1 STEPS FROM \"{id}\" OVER followed_by YIELD dst(edge) AS followed, src(edge) AS src")
    follow_neighbour = []
    if len(following_node_result) == 1:
        follow_neighbour.append(following_node_result[0]['following'])
    if len(followed_node_result) == 1:
        follow_neighbour.append(followed_node_result[0]['followed'])
    # print(followed_node_result)
    # print(following_node_result)
    return follow_neighbour

def search_related_neighbour(id, selected_dataset):
    graph_store = CustomNebulaPropertyGraphStore(space=selected_dataset, overwrite=False)
    # print(f"id: {id}")
    nodes = graph_store.structured_query(f"GO 1 STEPS FROM \"{id}\" OVER related YIELD dst(edge) as Neighbour, properties(edge).score as score | ORDER BY $-.score DESC")
    # print(f"nodes:{nodes}") 
    return [neighbour_node['Neighbour'] for neighbour_node in nodes]

def search_entities_neighbour(id, selected_dataset, selected_files, limit):
    graph_store = CustomNebulaPropertyGraphStore(space=selected_dataset, overwrite=False)
    selected_file_query_str = ', '.join([f'\"{name}\"' for name in selected_files])
    nodes = graph_store.structured_query(f"GO 1 STEPS FROM \"{id}\" OVER contains,mentions YIELD dst(edge) AS dst | GO 1 STEPS FROM $-.dst OVER contains, mentions REVERSELY WHERE src(edge) != \"{id}\" AND properties($$).parent IN [{selected_file_query_str}] YIELD src(edge) as Neighbour | GROUP BY $-.Neighbour YIELD $-.Neighbour as Neighbour, count(*) as Score | ORDER BY $-.Score DESC | LIMIT {limit}")
    return [neighbour_node['Neighbour'] for neighbour_node in nodes if neighbour_node['Neighbour']]
    #return [neighbour_node['Neighbour'] for neighbour_node in nodes if neighbour_node['Neighbour'].split('_')[0] in selected_files]

def search_neighbour(id, selected_dataset, selected_files, entities_neighbour_limit):
    follow_neighbour = search_follow_neighbour(id, selected_dataset)
    related_neighbour = search_related_neighbour(id, selected_dataset)
    # entities_neighbour = search_entities_neighbour(id, selected_dataset, selected_files, entities_neighbour_limit)
    print(f"follow: {follow_neighbour}")
    print(f"related: {related_neighbour}")
    # print(f"entities: {entities_neighbour}")
    neighbour_list = [id] + follow_neighbour + related_neighbour
    neighbour_list.sort()
    return neighbour_list #+ entities_neighbour
    # return related_neighbour + entities_neighbour


# Tony's version of search_neighbour, not used
# def search_neighbour(id, selected_dataset):
#     graph_store = CustomNebulaPropertyGraphStore(space=selected_dataset,overwrite=False)
#     nodes = graph_store.structured_query(f"GO 1 TO 2 STEPS FROM \"{id}\" OVER contains,mentions YIELD src(edge) AS src, dst(edge) AS dst")
#     src_list = set([node["src"] for node in nodes])
#     dst_list = set([node["dst"] for node in nodes])

#     #only take the leaves
#     dst_list = list(dst_list - src_list)
#     # In one query:
#     #ensure filter out non-entities, then find all chunk/image that points to them in 1 edge.
#     if dst_list:
#         insert_query = f"match (n:Entity__) where id(n) in {dst_list} match (m)-->(n) return id(m) as id"
#         result = graph_store.structured_query(insert_query)
#         result = [element["id"] for element in result]
#     else:
#         result = []
#     graph_store._client.close()
#     return result
#     #return result


